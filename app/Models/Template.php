<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'file_path',
        'file_original_name',
        'file_size',
        'demo_video_path',
        'demo_video_original_name',
        'demo_video_size',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the file URL attribute.
     */
    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get the demo video URL attribute.
     */
    public function getDemoVideoUrlAttribute()
    {
        return $this->demo_video_path ? asset('storage/' . $this->demo_video_path) : null;
    }

    /**
     * Get the user who created this template.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this template.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

