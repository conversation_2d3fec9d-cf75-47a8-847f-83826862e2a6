<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\ServiceController;
use App\Http\Controllers\Api\V1\ToolController;
use App\Http\Controllers\Api\V1\TemplateController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API Version 1
Route::prefix('v1')->group(function () {
    
    // Authentication Routes (Public)
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        
        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::get('me', [AuthController::class, 'me']);
            Route::post('logout', [AuthController::class, 'logout']);
        });
    });

    // Protected routes
    Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
        
        // Admin-only User Management
        Route::prefix('admin')->middleware('role:admin')->group(function () {
            // User verification management
            Route::get('users', [AuthController::class, 'getAllUsers']);
            Route::post('users/verify', [AuthController::class, 'verifyUser']);
            Route::post('users/unverify', [AuthController::class, 'unverifyUser']);
            Route::delete('users', [AuthController::class, 'deleteUser']);
        });
        
        // Users & Roles (Admin Only)
        Route::middleware('role:admin')->group(function () {
            Route::apiResource('users', UserController::class);
        });
        
        // Collaborateurs Management (Admin Only)
        Route::prefix('collaborateurs')->middleware('role:admin')->group(function () {
            Route::get('/', [UserController::class, 'collaborateurs']);
            Route::post('/', [UserController::class, 'createCollaborateur']);
            Route::put('{id}', [UserController::class, 'update']);
            Route::delete('{id}', [UserController::class, 'destroy']);
        });

        // Services & Tools (Available to verified users)
        Route::middleware('verified')->group(function () {
            Route::apiResource('services', ServiceController::class);
            Route::prefix('services/{service}')->group(function () {
                Route::get('tools', [ToolController::class, 'index']);
                Route::post('tools', [ToolController::class, 'store']);
            });
            Route::apiResource('tools', ToolController::class)->except(['index', 'store']);

            // Templates (Files + Demo Video)
            Route::apiResource('templates', TemplateController::class);
            Route::get('templates/{template}/download', [TemplateController::class, 'download']);
        });
    });
});

// Legacy route for backward compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Test route without role middleware
    Route::get('test-auth', function (Request $request) {
        return response()->json([
            'user' => $request->user()->name,
            'roles' => $request->user()->getRoleNames(),
            'is_admin' => $request->user()->hasRole('admin')
        ]);
    });
    
    // Test admin routes without middleware
    Route::prefix('debug-admin')->group(function () {
        Route::get('users', [AuthController::class, 'getAllUsers']);
        Route::post('users/verify', [AuthController::class, 'verifyUser']);
        Route::post('users/unverify', [AuthController::class, 'unverifyUser']);
    });
});