<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->string('file_original_name')->nullable()->after('file_path');
            $table->bigInteger('file_size')->nullable()->after('file_original_name');
            $table->string('demo_video_original_name')->nullable()->after('demo_video_path');
            $table->bigInteger('demo_video_size')->nullable()->after('demo_video_original_name');
            $table->unsignedBigInteger('created_by')->nullable()->after('is_active');
            $table->unsignedBigInteger('updated_by')->nullable()->after('created_by');

            // Add foreign key constraints if users table exists
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            $table->dropColumn([
                'file_original_name',
                'file_size',
                'demo_video_original_name',
                'demo_video_size',
                'created_by',
                'updated_by'
            ]);
        });
    }
};
