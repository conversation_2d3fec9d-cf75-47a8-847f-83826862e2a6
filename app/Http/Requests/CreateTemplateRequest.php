<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:ppt,pptx,doc,docx,pdf',
            'is_active' => 'sometimes|boolean',
            'file' => [
                'required',
                'file',
                'mimes:ppt,pptx,doc,docx,pdf',
                'max:' . (1024 * 50), // 50MB
            ],
            'demo_video' => [
                'nullable',
                'file',
                'mimes:mp4,mov,avi',
                'max:' . (1024 * 100), // 100MB
            ],
        ];
    }

    public function messages()
    {
        return [
            'file.required' => 'Template file is required',
            'file.max' => 'Template file cannot exceed 50MB',
            'file.mimes' => 'Only PowerPoint, Word and PDF files are allowed',
            'demo_video.max' => 'Demo video cannot exceed 100MB',
            'demo_video.mimes' => 'Only MP4, MOV and AVI videos are allowed',
        ];
    }
}

