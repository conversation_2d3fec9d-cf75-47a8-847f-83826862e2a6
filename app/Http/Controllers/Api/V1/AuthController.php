<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;

class AuthController extends Controller
{
    /**
     * Register a new user
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        try {
            // Check if phone already exists
            if (User::where('phone', $request->phone)->exists()) {
                return $this->errorResponse(
                    'Phone number already registered',
                    ['phone' => ['The phone number is already registered.']],
                    422
                );
            }

            $user = User::create([
                'name' => $request->name,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'is_verified' => false,
            ]);

            // Assign default role
            $user->assignRole($request->role ?? 'user');

            return $this->successResponse(
                'User registered successfully. Your account is pending admin verification.',
                new UserResource($user),
                201
            );

        } catch (ValidationException $e) {
            return $this->errorResponse(
                'Validation failed',
                $e->errors(),
                422
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Registration failed',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Login user
     */
    public function login(LoginRequest $request): JsonResponse
    {
        try {
            $credentials = $request->only('phone', 'password');

            if (!Auth::attempt($credentials)) {
                return $this->errorResponse(
                    'Invalid credentials',
                    ['credentials' => ['Invalid phone number or password.']],
                    401
                );
            }

            $user = Auth::user();

            if (!$user->is_verified) {
                return $this->errorResponse(
                    'Account not verified',
                    ['verification' => ['Your account is pending admin verification. Please contact support.']],
                    403
                );
            }

            $token = $user->createToken('auth_token')->plainTextToken;

            return $this->successResponse(
                'Login successful',
                [
                    'user' => new UserResource($user),
                    'token' => $token,
                    'token_type' => 'Bearer'
                ]
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Login failed',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Get current user
     */
    public function me(Request $request): JsonResponse
    {
        try {
            return $this->successResponse(
                'User retrieved successfully',
                new UserResource($request->user())
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to retrieve user',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return $this->successResponse('Logged out successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Logout failed',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Get all users with filters (Admin only)
     */
    public function getAllUsers(Request $request): JsonResponse
    {
        try {
            // Check if user is admin
            if (!$request->user()->hasRole('admin')) {
                return $this->errorResponse(
                    'Unauthorized access',
                    ['permission' => ['Admin access required.']],
                    403
                );
            }

            $validator = Validator::make($request->all(), [
                'status' => 'sometimes|in:verified,unverified,all',
                'search' => 'sometimes|string|max:255',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    $validator->errors()->toArray(),
                    422
                );
            }

            $query = User::with('roles');

            // Filter by verification status
            if ($request->status === 'verified') {
                $query->where('is_verified', true);
            } elseif ($request->status === 'unverified') {
                $query->where('is_verified', false);
            }

            // Search functionality
            if ($request->search) {
                $query->where(function($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('phone', 'like', '%' . $request->search . '%');
                });
            }

            $users = $query->orderBy('created_at', 'desc')
                          ->paginate($request->per_page ?? 15);

            return $this->successResponse(
                'Users retrieved successfully',
                [
                    'users' => UserResource::collection($users->items()),
                    'pagination' => [
                        'current_page' => $users->currentPage(),
                        'last_page' => $users->lastPage(),
                        'per_page' => $users->perPage(),
                        'total' => $users->total()
                    ]
                ]
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to retrieve users',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Verify user manually (Admin only)
     */
    public function verifyUser(Request $request): JsonResponse
    {
        try {
            // Check if user is admin
            if (!$request->user()->hasRole('admin')) {
                return $this->errorResponse(
                    'Unauthorized access',
                    ['permission' => ['Admin access required.']],
                    403
                );
            }

            $validator = Validator::make($request->all(), [
                'user_id' => 'required|integer|exists:users,id'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    $validator->errors()->toArray(),
                    422
                );
            }

            $user = User::find($request->user_id);

            if ($user->is_verified) {
                return $this->errorResponse(
                    'User already verified',
                    ['user' => ['This user is already verified.']],
                    400
                );
            }

            $user->update([
                'is_verified' => true,
                'phone_verified_at' => Carbon::now()
            ]);

            return $this->successResponse(
                'User verified successfully',
                new UserResource($user->fresh())
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to verify user',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Unverify user (Admin only)
     */
    public function unverifyUser(Request $request): JsonResponse
    {
        try {
            // Check if user is admin
            if (!$request->user()->hasRole('admin')) {
                return $this->errorResponse(
                    'Unauthorized access',
                    ['permission' => ['Admin access required.']],
                    403
                );
            }

            $validator = Validator::make($request->all(), [
                'user_id' => 'required|integer|exists:users,id'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    $validator->errors()->toArray(),
                    422
                );
            }

            $user = User::find($request->user_id);

            // Prevent admin from unverifying themselves
            if ($user->id === $request->user()->id) {
                return $this->errorResponse(
                    'Cannot unverify yourself',
                    ['user' => ['You cannot unverify your own account.']],
                    400
                );
            }

            if (!$user->is_verified) {
                return $this->errorResponse(
                    'User already unverified',
                    ['user' => ['This user is already unverified.']],
                    400
                );
            }

            $user->update([
                'is_verified' => false,
                'phone_verified_at' => null
            ]);

            // Revoke all user tokens
            $user->tokens()->delete();

            return $this->successResponse(
                'User unverified successfully',
                new UserResource($user->fresh())
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to unverify user',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Delete user (Admin only)
     */
    public function deleteUser(Request $request): JsonResponse
    {
        try {
            // Check if user is admin
            if (!$request->user()->hasRole('admin')) {
                return $this->errorResponse(
                    'Unauthorized access',
                    ['permission' => ['Admin access required.']],
                    403
                );
            }

            $validator = Validator::make($request->all(), [
                'user_id' => 'required|integer|exists:users,id'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    $validator->errors()->toArray(),
                    422
                );
            }

            $user = User::find($request->user_id);

            // Prevent admin from deleting themselves
            if ($user->id === $request->user()->id) {
                return $this->errorResponse(
                    'Cannot delete yourself',
                    ['user' => ['You cannot delete your own account.']],
                    400
                );
            }

            // Store user name before deletion
            $userName = $user->name;
            
            // Revoke all user tokens before deletion
            $user->tokens()->delete();
            
            // Delete user
            $user->delete();

            return $this->successResponse(
                "User '{$userName}' deleted successfully"
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to delete user',
                ['general' => [$e->getMessage()]],
                500
            );
        }
    }

    /**
     * Return success response
     */
    private function successResponse(string $message, $data = null, int $status = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status);
    }

    /**
     * Return error response
     */
    private function errorResponse(string $message, array $errors = [], int $status = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $status);
    }
}