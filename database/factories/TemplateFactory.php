<?php

namespace Database\Factories;

use App\Models\Template;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Template>
 */
class TemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Template::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['ppt', 'pptx', 'doc', 'docx', 'pdf'];
        $type = $this->faker->randomElement($types);

        return [
            'name' => $this->faker->words(3, true) . ' Template',
            'description' => $this->faker->sentence(),
            'type' => $type,
            'file_path' => 'templates/files/' . $this->faker->uuid() . '.' . $type,
            'file_original_name' => $this->faker->words(2, true) . '.' . $type,
            'file_size' => $this->faker->numberBetween(1000, 50000000), // 1KB to 50MB
            'demo_video_path' => $this->faker->boolean(70) ? 'templates/videos/' . $this->faker->uuid() . '.mp4' : null,
            'demo_video_original_name' => $this->faker->boolean(70) ? $this->faker->words(2, true) . '.mp4' : null,
            'demo_video_size' => $this->faker->boolean(70) ? $this->faker->numberBetween(5000000, 100000000) : null, // 5MB to 100MB
            'is_active' => $this->faker->boolean(80),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the template has no demo video.
     */
    public function withoutVideo(): static
    {
        return $this->state(fn (array $attributes) => [
            'demo_video_path' => null,
        ]);
    }

    /**
     * Indicate that the template has a demo video.
     */
    public function withVideo(): static
    {
        return $this->state(fn (array $attributes) => [
            'demo_video_path' => 'templates/videos/' . $this->faker->uuid() . '.mp4',
        ]);
    }

    /**
     * Create a template of a specific type.
     */
    public function ofType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => $type,
            'file_path' => 'templates/files/' . $this->faker->uuid() . '.' . $type,
        ]);
    }
}
