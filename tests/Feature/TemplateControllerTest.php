<?php

namespace Tests\Feature;

use App\Models\Template;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class TemplateControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $collaborateur;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'collaborateur']);
        Role::create(['name' => 'user']);

        // Create test users
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        $this->collaborateur = User::factory()->create();
        $this->collaborateur->assignRole('collaborateur');

        $this->user = User::factory()->create();
        $this->user->assignRole('user');

        // Setup storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function test_index_returns_templates_for_authenticated_user()
    {
        Sanctum::actingAs($this->user);

        $templates = Template::factory()->count(3)->create();

        $response = $this->getJson('/api/v1/templates');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'templates' => [
                            '*' => [
                                'id',
                                'name',
                                'description',
                                'type',
                                'file_url',
                                'demo_video_url',
                                'is_active',
                                'can_download',
                                'created_at',
                                'updated_at'
                            ]
                        ],
                        'pagination' => [
                            'current_page',
                            'last_page',
                            'per_page',
                            'total',
                            'from',
                            'to'
                        ]
                    ]
                ]);
    }

    /** @test */
    public function test_index_filters_by_type()
    {
        Sanctum::actingAs($this->user);

        Template::factory()->create(['type' => 'pdf']);
        Template::factory()->create(['type' => 'docx']);
        Template::factory()->create(['type' => 'ppt']);

        $response = $this->getJson('/api/v1/templates?type=pdf');

        $response->assertStatus(200);
        $data = $response->json('data.templates');
        $this->assertCount(1, $data);
        $this->assertEquals('pdf', $data[0]['type']);
    }

    /** @test */
    public function test_index_filters_by_is_active()
    {
        Sanctum::actingAs($this->user);

        Template::factory()->create(['is_active' => true]);
        Template::factory()->create(['is_active' => false]);

        $response = $this->getJson('/api/v1/templates?is_active=1');

        $response->assertStatus(200);
        $data = $response->json('data.templates');
        $this->assertCount(1, $data);
        $this->assertTrue($data[0]['is_active']);
    }

    /** @test */
    public function test_index_searches_templates()
    {
        Sanctum::actingAs($this->user);

        Template::factory()->create(['name' => 'Business Template', 'description' => 'For business use']);
        Template::factory()->create(['name' => 'Personal Template', 'description' => 'For personal use']);

        $response = $this->getJson('/api/v1/templates?search=business');

        $response->assertStatus(200);
        $data = $response->json('data.templates');
        $this->assertCount(1, $data);
        $this->assertStringContainsString('Business', $data[0]['name']);
    }

    /** @test */
    public function test_index_sorts_templates()
    {
        Sanctum::actingAs($this->user);

        Template::factory()->create(['name' => 'Z Template']);
        Template::factory()->create(['name' => 'A Template']);

        $response = $this->getJson('/api/v1/templates?sort_by=name&sort_order=asc');

        $response->assertStatus(200);
        $data = $response->json('data.templates');
        $this->assertEquals('A Template', $data[0]['name']);
        $this->assertEquals('Z Template', $data[1]['name']);
    }

    /** @test */
    public function test_index_validates_parameters()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/templates?type=invalid&per_page=101');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['type', 'per_page']);
    }

    /** @test */
    public function test_index_requires_authentication()
    {
        $response = $this->getJson('/api/v1/templates');

        $response->assertStatus(401);
    }

    /** @test */
    public function test_store_creates_template_for_admin()
    {
        Sanctum::actingAs($this->admin);

        $file = UploadedFile::fake()->create('template.pdf', 1000, 'application/pdf');
        $video = UploadedFile::fake()->create('demo.mp4', 5000, 'video/mp4');

        $data = [
            'name' => 'Test Template',
            'description' => 'Test Description',
            'type' => 'pdf',
            'is_active' => true,
            'file' => $file,
            'demo_video' => $video
        ];

        $response = $this->postJson('/api/v1/templates', $data);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'type',
                        'file_url',
                        'demo_video_url',
                        'is_active'
                    ]
                ]);

        $this->assertDatabaseHas('templates', [
            'name' => 'Test Template',
            'description' => 'Test Description',
            'type' => 'pdf',
            'is_active' => true
        ]);

        // Verify files were stored
        $template = Template::where('name', 'Test Template')->first();
        Storage::disk('public')->assertExists($template->file_path);
        Storage::disk('public')->assertExists($template->demo_video_path);
    }

    /** @test */
    public function test_store_requires_admin_role()
    {
        Sanctum::actingAs($this->user);

        $file = UploadedFile::fake()->create('template.pdf', 1000, 'application/pdf');

        $data = [
            'name' => 'Test Template',
            'type' => 'pdf',
            'file' => $file
        ];

        $response = $this->postJson('/api/v1/templates', $data);

        $response->assertStatus(403);
    }

    /** @test */
    public function test_store_validates_required_fields()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/v1/templates', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'type', 'file']);
    }

    /** @test */
    public function test_store_validates_file_type()
    {
        Sanctum::actingAs($this->admin);

        $file = UploadedFile::fake()->create('template.txt', 1000, 'text/plain');

        $data = [
            'name' => 'Test Template',
            'type' => 'pdf',
            'file' => $file
        ];

        $response = $this->postJson('/api/v1/templates', $data);

        $response->assertStatus(422);
    }

    /** @test */
    public function test_store_creates_template_without_demo_video()
    {
        Sanctum::actingAs($this->admin);

        $file = UploadedFile::fake()->create('template.pdf', 1000, 'application/pdf');

        $data = [
            'name' => 'Test Template',
            'description' => 'Test Description',
            'type' => 'pdf',
            'file' => $file
        ];

        $response = $this->postJson('/api/v1/templates', $data);

        $response->assertStatus(201);
        
        $template = Template::where('name', 'Test Template')->first();
        $this->assertNull($template->demo_video_path);
    }

    /** @test */
    public function test_show_returns_template_for_authenticated_user()
    {
        Sanctum::actingAs($this->user);

        $template = Template::factory()->create();

        $response = $this->getJson("/api/v1/templates/{$template->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'type',
                        'file_url',
                        'demo_video_url',
                        'is_active',
                        'can_download',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $template->id,
                        'name' => $template->name
                    ]
                ]);
    }

    /** @test */
    public function test_show_returns_404_for_nonexistent_template()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/templates/999');

        $response->assertStatus(404);
    }

    /** @test */
    public function test_show_requires_authentication()
    {
        $template = Template::factory()->create();

        $response = $this->getJson("/api/v1/templates/{$template->id}");

        $response->assertStatus(401);
    }

    /** @test */
    public function test_update_modifies_template_for_admin()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original Description'
        ]);

        $newFile = UploadedFile::fake()->create('new_template.pdf', 1000, 'application/pdf');

        $data = [
            'name' => 'Updated Name',
            'description' => 'Updated Description',
            'type' => 'pdf',
            'is_active' => false,
            'file' => $newFile
        ];

        $response = $this->putJson("/api/v1/templates/{$template->id}", $data);

        $response->assertStatus(200)
                ->assertJson([
                    'data' => [
                        'name' => 'Updated Name',
                        'description' => 'Updated Description',
                        'is_active' => false
                    ]
                ]);

        $this->assertDatabaseHas('templates', [
            'id' => $template->id,
            'name' => 'Updated Name',
            'description' => 'Updated Description',
            'is_active' => false
        ]);
    }

    /** @test */
    public function test_update_requires_admin_role()
    {
        Sanctum::actingAs($this->user);

        $template = Template::factory()->create();

        $data = ['name' => 'Updated Name'];

        $response = $this->putJson("/api/v1/templates/{$template->id}", $data);

        $response->assertStatus(403);
    }

    /** @test */
    public function test_update_validates_fields()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create();

        $data = [
            'type' => 'invalid_type',
            'name' => str_repeat('a', 300) // Too long
        ];

        $response = $this->putJson("/api/v1/templates/{$template->id}", $data);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['type', 'name']);
    }

    /** @test */
    public function test_update_replaces_files()
    {
        Sanctum::actingAs($this->admin);

        // Create template with initial files
        $oldFile = UploadedFile::fake()->create('old_template.pdf', 1000, 'application/pdf');
        $oldVideo = UploadedFile::fake()->create('old_demo.mp4', 5000, 'video/mp4');

        $template = Template::factory()->create();

        // Simulate existing files
        $oldFilePath = 'templates/files/old_file.pdf';
        $oldVideoPath = 'templates/videos/old_video.mp4';
        Storage::disk('public')->put($oldFilePath, 'old file content');
        Storage::disk('public')->put($oldVideoPath, 'old video content');

        $template->update([
            'file_path' => $oldFilePath,
            'demo_video_path' => $oldVideoPath
        ]);

        // Update with new files
        $newFile = UploadedFile::fake()->create('new_template.pdf', 1000, 'application/pdf');
        $newVideo = UploadedFile::fake()->create('new_demo.mp4', 5000, 'video/mp4');

        $data = [
            'file' => $newFile,
            'demo_video' => $newVideo
        ];

        $response = $this->putJson("/api/v1/templates/{$template->id}", $data);

        $response->assertStatus(200);

        // Verify old files are deleted and new files exist
        Storage::disk('public')->assertMissing($oldFilePath);
        Storage::disk('public')->assertMissing($oldVideoPath);

        $template->refresh();
        Storage::disk('public')->assertExists($template->file_path);
        Storage::disk('public')->assertExists($template->demo_video_path);
    }

    /** @test */
    public function test_destroy_deletes_template_for_admin()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create();

        // Create files to be deleted
        $filePath = 'templates/files/test_file.pdf';
        $videoPath = 'templates/videos/test_video.mp4';
        Storage::disk('public')->put($filePath, 'test file content');
        Storage::disk('public')->put($videoPath, 'test video content');

        $template->update([
            'file_path' => $filePath,
            'demo_video_path' => $videoPath
        ]);

        $response = $this->deleteJson("/api/v1/templates/{$template->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => "Template '{$template->name}' deleted successfully"
                ]);

        $this->assertDatabaseMissing('templates', ['id' => $template->id]);

        // Verify files are deleted
        Storage::disk('public')->assertMissing($filePath);
        Storage::disk('public')->assertMissing($videoPath);
    }

    /** @test */
    public function test_destroy_requires_admin_role()
    {
        Sanctum::actingAs($this->user);

        $template = Template::factory()->create();

        $response = $this->deleteJson("/api/v1/templates/{$template->id}");

        $response->assertStatus(403);
    }

    /** @test */
    public function test_destroy_returns_404_for_nonexistent_template()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->deleteJson('/api/v1/templates/999');

        $response->assertStatus(404);
    }

    /** @test */
    public function test_download_returns_file_for_admin()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create();

        // Create a test file
        $filePath = 'templates/files/test_download.pdf';
        $fileContent = 'test file content for download';
        Storage::disk('public')->put($filePath, $fileContent);

        $template->update([
            'file_path' => $filePath,
            'file_original_name' => 'original_name.pdf'
        ]);

        $response = $this->get("/api/v1/templates/{$template->id}/download");

        $response->assertStatus(200);
        $response->assertHeader('content-disposition');

        // For file downloads, we check if the response is a BinaryFileResponse
        $this->assertInstanceOf(\Symfony\Component\HttpFoundation\BinaryFileResponse::class, $response->baseResponse);
    }

    /** @test */
    public function test_download_returns_file_for_collaborateur()
    {
        Sanctum::actingAs($this->collaborateur);

        $template = Template::factory()->create();

        // Create a test file
        $filePath = 'templates/files/test_download.pdf';
        $fileContent = 'test file content for download';
        Storage::disk('public')->put($filePath, $fileContent);

        $template->update([
            'file_path' => $filePath,
            'file_original_name' => 'original_name.pdf'
        ]);

        $response = $this->get("/api/v1/templates/{$template->id}/download");

        $response->assertStatus(200);
    }

    /** @test */
    public function test_download_requires_admin_or_collaborateur_role()
    {
        Sanctum::actingAs($this->user);

        $template = Template::factory()->create();

        $response = $this->get("/api/v1/templates/{$template->id}/download");

        $response->assertStatus(403);
    }

    /** @test */
    public function test_download_returns_404_when_no_file_attached()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create();
        // Update to remove file_path after creation
        $template->update(['file_path' => null]);

        $response = $this->get("/api/v1/templates/{$template->id}/download");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'No file available'
                ]);
    }

    /** @test */
    public function test_download_returns_404_when_file_not_found_on_disk()
    {
        Sanctum::actingAs($this->admin);

        $template = Template::factory()->create([
            'file_path' => 'templates/files/nonexistent.pdf'
        ]);

        $response = $this->get("/api/v1/templates/{$template->id}/download");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'File not found'
                ]);
    }

    /** @test */
    public function test_download_requires_authentication()
    {
        $template = Template::factory()->create();

        $response = $this->getJson("/api/v1/templates/{$template->id}/download");

        // The route is protected by auth:sanctum middleware, so should return 401
        $response->assertStatus(401);
    }

    /** @test */
    public function test_can_download_flag_in_template_resource()
    {
        // Test for admin
        Sanctum::actingAs($this->admin);
        $template = Template::factory()->create();
        $response = $this->getJson("/api/v1/templates/{$template->id}");
        $response->assertJson(['data' => ['can_download' => true]]);

        // Test for collaborateur
        Sanctum::actingAs($this->collaborateur);
        $response = $this->getJson("/api/v1/templates/{$template->id}");
        $response->assertJson(['data' => ['can_download' => true]]);

        // Test for regular user
        Sanctum::actingAs($this->user);
        $response = $this->getJson("/api/v1/templates/{$template->id}");
        $response->assertJson(['data' => ['can_download' => false]]);
    }
}
