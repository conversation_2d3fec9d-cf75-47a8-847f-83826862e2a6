<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Log;

class TestWhatsApp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test {phone} {--message=Test message from SADEMY API}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WhatsApp integration with Twilio';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $message = $this->option('message');

        $this->info("Testing WhatsApp integration...");
        $this->info("Phone: {$phone}");
        $this->info("Message: {$message}");
        
        // Check Twilio configuration
        $accountSid = config('services.twilio.account_sid');
        $authToken = config('services.twilio.auth_token');
        $fromNumber = config('services.twilio.whatsapp_from');
        
        $this->info("\nTwilio Configuration:");
        $this->info("Account SID: " . ($accountSid ? 'Configured (' . substr($accountSid, 0, 10) . '...)' : 'NOT CONFIGURED'));
        $this->info("Auth Token: " . ($authToken ? 'Configured (' . substr($authToken, 0, 10) . '...)' : 'NOT CONFIGURED'));
        $this->info("WhatsApp From: " . ($fromNumber ?: 'NOT CONFIGURED'));
        
        if (empty($accountSid) || empty($authToken) || empty($fromNumber)) {
            $this->warn("\n⚠️  Twilio credentials are not fully configured.");
            $this->warn("The message will be logged instead of sent.");
            $this->info("\nTo configure Twilio, add these to your .env file:");
            $this->info("TWILIO_ACCOUNT_SID=your_account_sid");
            $this->info("TWILIO_AUTH_TOKEN=your_auth_token");
            $this->info("TWILIO_WHATSAPP_FROM=whatsapp:+***********  # Twilio Sandbox number");
        } else {
            $this->info("\n📋 Twilio WhatsApp Setup Notes:");
            $this->info("• For testing: Use Twilio WhatsApp Sandbox number: whatsapp:+***********");
            $this->info("• For production: Use your verified WhatsApp Business number");
            $this->info("• Current From number: " . $fromNumber);

            if (!str_starts_with($fromNumber, 'whatsapp:')) {
                $this->warn("⚠️  WhatsApp From number should start with 'whatsapp:' prefix");
                $this->info("Example: whatsapp:+***********");
            }
        }
        
        $this->info("\nSending message...");
        
        $whatsappService = new WhatsAppService();
        $result = $whatsappService->sendVerificationCode($phone, '123456');
        
        if ($result) {
            $this->info("✅ Message sent successfully!");
            
            if (empty($accountSid) || empty($authToken)) {
                $this->info("📝 Check the Laravel log file for the logged message:");
                $this->info("tail -f storage/logs/laravel.log | grep 'WhatsApp message'");
            } else {
                $this->info("📱 Check your WhatsApp for the message.");
            }
        } else {
            $this->error("❌ Failed to send message.");
            $this->info("Check the Laravel log file for error details:");
            $this->info("tail -f storage/logs/laravel.log");
        }
        
        return 0;
    }
}
