<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\VerificationCode;

class ShowVerificationCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verification:show {phone?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show verification codes for testing purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');

        if ($phone) {
            // Show codes for specific phone number
            $codes = VerificationCode::where('phone', $phone)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            if ($codes->isEmpty()) {
                $this->info("No verification codes found for phone: {$phone}");
                return;
            }

            $this->info("Verification codes for {$phone}:");
        } else {
            // Show recent codes for all phones
            $codes = VerificationCode::orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            if ($codes->isEmpty()) {
                $this->info("No verification codes found.");
                return;
            }

            $this->info("Recent verification codes:");
        }

        $this->table(
            ['Phone', 'Code', 'Created', 'Expires', 'Used'],
            $codes->map(function ($code) {
                return [
                    $code->phone,
                    $code->code,
                    $code->created_at->format('Y-m-d H:i:s'),
                    $code->expires_at->format('Y-m-d H:i:s'),
                    $code->used_at ? 'Yes' : 'No'
                ];
            })
        );
    }
}
